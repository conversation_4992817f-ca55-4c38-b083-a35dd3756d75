#!/usr/bin/env ts-node

/**
 * Debug script to check Docker environment setup
 * Usage: docker-compose exec api npm run script:debug-docker
 */

import * as fs from 'fs';
import * as path from 'path';

console.log('🔍 PrintNet Docker Environment Debug\n');

// Check current working directory
console.log('📁 Current working directory:', process.cwd());

// Check if scripts directory exists
const scriptsDir = path.join(process.cwd(), 'src', 'scripts');
console.log('📂 Scripts directory path:', scriptsDir);
console.log('📂 Scripts directory exists:', fs.existsSync(scriptsDir));

if (fs.existsSync(scriptsDir)) {
  const files = fs.readdirSync(scriptsDir);
  console.log('📄 Files in scripts directory:', files);
}

// Check if dist directory exists
const distDir = path.join(process.cwd(), 'dist');
console.log('📂 Dist directory path:', distDir);
console.log('📂 Dist directory exists:', fs.existsSync(distDir));

if (fs.existsSync(distDir)) {
  const distScriptsDir = path.join(distDir, 'scripts');
  console.log('📂 Dist scripts directory exists:', fs.existsSync(distScriptsDir));
  
  if (fs.existsSync(distScriptsDir)) {
    const distFiles = fs.readdirSync(distScriptsDir);
    console.log('📄 Files in dist/scripts directory:', distFiles);
  }
}

// Check environment variables
console.log('\n🌍 Environment Variables:');
console.log('NODE_ENV:', process.env.NODE_ENV);
console.log('DB_HOST:', process.env.DB_HOST);
console.log('DB_PORT:', process.env.DB_PORT);
console.log('DB_DATABASE:', process.env.DB_DATABASE);
console.log('DB_USERNAME:', process.env.DB_USERNAME ? '[SET]' : '[NOT SET]');
console.log('DB_PASSWORD:', process.env.DB_PASSWORD ? '[SET]' : '[NOT SET]');

// Check Node.js and npm versions
console.log('\n🔧 System Info:');
console.log('Node.js version:', process.version);
console.log('Platform:', process.platform);
console.log('Architecture:', process.arch);

// Check if ts-node is available
try {
  const tsNodeVersion = require('ts-node/package.json').version;
  console.log('ts-node version:', tsNodeVersion);
} catch (error) {
  console.log('ts-node: NOT AVAILABLE');
}

// Check if TypeScript is available
try {
  const tsVersion = require('typescript/package.json').version;
  console.log('TypeScript version:', tsVersion);
} catch (error) {
  console.log('TypeScript: NOT AVAILABLE');
}

console.log('\n✅ Debug completed!');
