# PrintNet Backend Scripts

This directory contains utility scripts for managing the PrintNet backend application.

## Available Scripts

### 1. Admin User Creation Script (`create-admin.ts`)

Creates an admin user with proper roles for accessing the admin dashboard.

#### Features

- ✅ Interactive mode with prompts for user input
- ✅ Command-line mode for automation
- ✅ Email validation
- ✅ Password strength validation (minimum 8 characters)
- ✅ Automatic password hashing using bcrypt
- ✅ Assigns both CLIENT and ADMIN roles
- ✅ Checks for existing users to prevent duplicates
- ✅ Comprehensive error handling
- ✅ Detailed success output with user information

#### Usage

##### Interactive Mode (Recommended)

```bash
npm run script:create-admin
```

This will prompt you for:
- Email address
- Password (minimum 8 characters)
- First name (optional)
- Last name (optional)
- Phone number (optional)

##### Command Line Mode

```bash
npm run script:create-admin -- --email <EMAIL> --password adminpass123 --first-name Admin --last-name User
```

##### Available Command Line Options

- `--email <email>` - Admin email address (required)
- `--password <password>` - Admin password (required, min 8 chars)
- `--first-name <name>` - Admin first name (optional)
- `--last-name <name>` - Admin last name (optional)
- `--phone <phone>` - Admin phone number (optional)
- `--help` - Show help message

##### Examples

**Minimal setup:**
```bash
npm run script:create-admin -- --email <EMAIL> --password adminpass123
```

**Full setup:**
```bash
npm run script:create-admin -- --email <EMAIL> --password adminpass123 --first-name John --last-name Doe --phone "+************"
```

**Show help:**
```bash
npm run script:create-admin -- --help
```

#### Output

Upon successful creation, the script will display:
- User ID
- Email address
- Full name
- Phone number
- Assigned roles
- Creation timestamp
- Account status

#### Error Handling

The script handles various error scenarios:
- Invalid email format
- Password too short
- User already exists
- Database connection issues
- Application initialization failures

#### Security Notes

- Passwords are automatically hashed using bcrypt with salt rounds of 10
- The script validates email format before creation
- Existing users are checked to prevent duplicates
- All sensitive operations are wrapped in try-catch blocks

## Prerequisites

Before running any scripts, ensure:

1. **Database is running** - PostgreSQL should be accessible
2. **Environment variables are set** - Check your `.env` files
3. **Dependencies are installed** - Run `npm install`
4. **Database migrations are up to date** - Run `npm run migration:run`

## Development

To add new scripts:

1. Create a new TypeScript file in this directory
2. Export the main class/function from `index.ts`
3. Add a corresponding npm script in `package.json`
4. Update this README with documentation

## Troubleshooting

### Common Issues

**"Cannot connect to database"**
- Check if PostgreSQL is running
- Verify database connection settings in your environment variables
- Ensure the database exists

**"User already exists"**
- The email address is already registered
- Use a different email or check existing users

**"Application context initialization failed"**
- Check for TypeScript compilation errors
- Verify all dependencies are installed
- Check environment configuration

**"Permission denied"**
- Ensure the script has execution permissions
- Check database user permissions

### Getting Help

If you encounter issues:
1. Check the error message carefully
2. Verify your environment setup
3. Check the application logs
4. Ensure all prerequisites are met

For additional help, refer to the main project documentation or contact the development team.
